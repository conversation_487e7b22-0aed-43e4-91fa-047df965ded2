#!/usr/bin/env python3
import json
import logging
import paho.mqtt.client as mqtt

# =============================
# KONFIGURACE
# =============================
MQTT_HOST = "mqtt.3idlabs.eu"
MQTT_PORT = 8883  # TLS port
MQTT_USERNAME = "b-003"
MQTT_PASSWORD = "ECE27m8YMR"
MQTT_CLIENT_ID = "b-003-listener"  # musí být unikátní

# Přihlásíme se na všechny response zprávy pro zařízení b-003
RESPONSE_TOPIC = "devices/b-003/responses/+"

# =============================
# LOGGING
# =============================
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(message)s")
logger = logging.getLogger(__name__)

# =============================
# CALLBACKY
# =============================
def on_connect(client, userdata, flags, rc):
    if rc == 0:
        logger.info("Connected to MQTT broker")
        client.subscribe(RESPONSE_TOPIC)
        logger.info(f"Subscribed to {RESPONSE_TOPIC}")
    else:
        logger.error(f"Connection failed with code {rc}")

def on_message(client, userdata, msg):
    try:
        payload = json.loads(msg.payload.decode())
        logger.info(f"Received on {msg.topic}: {payload}")
    except Exception as e:
        logger.error(f"Error decoding message: {e}")

# =============================
# HLAVNÍ KÓD
# =============================
def main():
    client = mqtt.Client(client_id=MQTT_CLIENT_ID)
    client.username_pw_set(MQTT_USERNAME, MQTT_PASSWORD)
    client.tls_set()  # TLS pro 8883

    client.on_connect = on_connect
    client.on_message = on_message

    client.connect(MQTT_HOST, MQTT_PORT, keepalive=60)
    client.loop_forever()

if __name__ == "__main__":
    main()
