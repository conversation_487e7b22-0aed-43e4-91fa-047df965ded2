#!/usr/bin/env python3
import json
import logging
import paho.mqtt.client as mqtt
import uuid


# =============================
# KONFIGURACE
# =============================
MQTT_HOST = "mqtt.3idlabs.eu"
MQTT_PORT = 8883  # TLS port
MQTT_USERNAME = "b-003"
MQTT_PASSWORD = "ECE27m8YMR"
MQTT_CLIENT_ID = "b-003-device-simulator"  # musí být unikátní

# Přihlásíme se na všechny command zprávy pro zařízení b-003
COMMAND_TOPIC = "devices/b-003/commands/+/+"

# =============================
# LOGGING
# =============================
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(message)s")
logger = logging.getLogger(__name__)

# =============================
# RESPONSE TEMPLATES
# =============================
def get_response_template(command_type, action):
    """Vrátí template pro odpověď na základě typu příkazu a akce"""
    templates = {
        "electronic": {
            "unlock": {
                "success": True,
                "section_id": 3,
                "message": "Section 3 opened successfully",
                "request_uuid": ""
            },
            "section_open": {
                "success": True,
                "message": "door was succesfully opened",
                "request_uuid": "",
                "section_id": 1
            },
            "door_state": {
                "success": True,
                "message": "door was succesfully cehcked",
                "request_uuid": "",
                "section_id": 1
            }
        },
        "system": {
            "reboot_device": {
                "success": True,
                "message": "Rebooting",
                "request_uuid": ""
            }
        },
        "sale": {
            "edit_reservation": {
                "success": True,
                "message": "Product edited succesfully",
                "request_uuid": "",
                "product_uuid": "fdsa43",
                "sections_id": 3,
                "status": 0,
                "price": 34
            },
            "reserve_product": {
                "success": True,
                "message": "Product reserved succesfully",
                "request_uuid": "",
                "product_uuid": "fdsa43",
                "sections_id": 3,
                "status": 0,
                "price": 34,
                "reservation_pin": 123456
            },
            "unreserve_product": {
                "success": True,
                "message": "Product unreserved succesfully",
                "request_uuid": "",
                "product_uuid": "fdsa43",
                "sections_id": 3,
                "status": 0,
                "price": 34
            }
        },
        "storage": {
            "edit_reservation": {
                "success": True,
                "message": "Storage edited succesfully",
                "request_uuid": "",
                "reservation_uuid": "fdsa43",
                "sections_id": 3,
                "status": 0,
                "reservation_pin": 123456
            }
        }
    }

    return templates.get(command_type, {}).get(action, None)

# =============================
# CALLBACKY
# =============================
def on_connect(client, userdata, flags, rc):
    if rc == 0:
        logger.info("Connected to MQTT broker as device simulator")
        client.subscribe(COMMAND_TOPIC)
        logger.info(f"Subscribed to {COMMAND_TOPIC}")
    else:
        logger.error(f"Connection failed with code {rc}")

def on_message(client, userdata, msg):
    try:
        # Parsuj topic
        topic_parts = msg.topic.split('/')
        if len(topic_parts) >= 5:
            device_id = topic_parts[1]  # b-003
            command_type = topic_parts[3]  # electronic, system, sale, storage
            action = topic_parts[4]  # unlock, section_open, etc.

            logger.info(f"Received command on {msg.topic}")

            # Parsuj payload
            command_payload = json.loads(msg.payload.decode())
            logger.info(f"Command payload: {command_payload}")

            # Získej template pro odpověď
            response_template = get_response_template(command_type, action)

            if response_template:
                # Vytvoř odpověď
                response = response_template.copy()

                # Nastav request_uuid z příkazu
                if "request_uuid" in command_payload:
                    response["request_uuid"] = command_payload["request_uuid"]
                else:
                    response["request_uuid"] = str(uuid.uuid4())[:8]

                # Pro některé příkazy nastav specifické hodnoty z příkazu
                if "section_id" in command_payload and "section_id" in response:
                    response["section_id"] = command_payload["section_id"]

                if "product_uuid" in command_payload and "product_uuid" in response:
                    response["product_uuid"] = command_payload["product_uuid"]

                # Vytvoř response topic
                if command_type == "system" and action == "reboot_device":
                    response_topic = f"devices/{device_id}/responses/system/reboot"
                else:
                    response_topic = f"devices/{device_id}/responses/{command_type}/{action}"

                # Pošli odpověď
                client.publish(response_topic, json.dumps(response))
                logger.info(f"Sent response to {response_topic}: {response}")
            else:
                logger.warning(f"No template found for {command_type}/{action}")

    except Exception as e:
        logger.error(f"Error processing message: {e}")

# =============================
# HLAVNÍ KÓD
# =============================
def main():
    client = mqtt.Client(client_id=MQTT_CLIENT_ID)
    client.username_pw_set(MQTT_USERNAME, MQTT_PASSWORD)
    client.tls_set()  # TLS pro 8883

    client.on_connect = on_connect
    client.on_message = on_message

    logger.info("Starting MQTT device simulator...")
    client.connect(MQTT_HOST, MQTT_PORT, keepalive=60)
    client.loop_forever()

if __name__ == "__main__":
    main()
