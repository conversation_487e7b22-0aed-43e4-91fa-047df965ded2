#!/usr/bin/env python3
import json
import logging
import paho.mqtt.client as mqtt
import time
import threading
import uuid

# =============================
# KONFIGURACE
# =============================
MQTT_HOST = "mqtt.3idlabs.eu"
MQTT_PORT = 8883  # TLS port
MQTT_USERNAME = "b-003"
MQTT_PASSWORD = "ECE27m8YMR"
MQTT_CLIENT_ID = "b-003-test-server"  # musí být unikátní

RESPONSE_TOPIC = "devices/b-003/responses/+/+"  # wildcard, zachytí všechny responses

# =============================
# LOGGING
# =============================
logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(message)s")
logger = logging.getLogger(__name__)

# =============================
# TEST COMMANDS
# =============================
TEST_COMMANDS = [
    {
        "topic": "devices/b-003/commands/electronic/unlock",
        "payload": {
            "request_uuid": str(uuid.uuid4())[:8],
            "section_id": 1
        },
        "description": "Electronic unlock command"
    },
    {
        "topic": "devices/b-003/commands/electronic/section_open",
        "payload": {
            "request_uuid": str(uuid.uuid4())[:8],
            "section_id": 1
        },
        "description": "Electronic section open command"
    },
    {
        "topic": "devices/b-003/commands/electronic/door_state",
        "payload": {
            "request_uuid": str(uuid.uuid4())[:8],
            "section_id": 1
        },
        "description": "Electronic door state command"
    },
    {
        "topic": "devices/b-003/commands/system/reboot_device",
        "payload": {
            "request_uuid": str(uuid.uuid4())[:8]
        },
        "description": "System reboot command"
    },
    {
        "topic": "devices/b-003/commands/sale/edit_reservation",
        "payload": {
            "request_uuid": str(uuid.uuid4())[:8],
            "product_uuid": "fdsa43",
            "section_id": 3,
            "status": 0,
            "price": 34
        },
        "description": "Sale edit reservation command"
    },
    {
        "topic": "devices/b-003/commands/sale/reserve_product",
        "payload": {
            "request_uuid": str(uuid.uuid4())[:8],
            "product_uuid": "fdsa43",
            "section_id": 3
        },
        "description": "Sale reserve product command"
    },
    {
        "topic": "devices/b-003/commands/sale/unreserve_product",
        "payload": {
            "request_uuid": str(uuid.uuid4())[:8],
            "product_uuid": "fdsa43",
            "section_id": 3,
            "reservation_pin": 123456
        },
        "description": "Sale unreserve product command"
    },
    {
        "topic": "devices/b-003/commands/storage/edit_reservation",
        "payload": {
            "request_uuid": str(uuid.uuid4())[:8],
            "reservation_uuid": "fdsa43",
            "reservation_pin": 123456,
            "section_id": 3,
            "status": 0
        },
        "description": "Storage edit reservation command"
    }
]

# =============================
# GLOBAL VARIABLES
# =============================
mqtt_client = None
responses_received = []
commands_sent = []

# =============================
# CALLBACKY
# =============================
def on_connect(client, userdata, flags, rc):
    if rc == 0:
        logger.info("Connected to MQTT broker as test server")
        client.subscribe(RESPONSE_TOPIC)
        logger.info(f"Subscribed to {RESPONSE_TOPIC}")
    else:
        logger.error(f"Connection failed with code {rc}")

def on_message(client, userdata, msg):
    try:
        payload = json.loads(msg.payload.decode())
        logger.info(f"✅ RESPONSE received on {msg.topic}: {payload}")
        responses_received.append({
            "topic": msg.topic,
            "payload": payload,
            "timestamp": time.time()
        })
    except Exception as e:
        logger.error(f"Error decoding response message: {e}")

# =============================
# TEST FUNCTIONS
# =============================
def send_test_command(command):
    """Pošle testovací příkaz"""
    try:
        logger.info(f"📤 SENDING: {command['description']}")
        logger.info(f"   Topic: {command['topic']}")
        logger.info(f"   Payload: {command['payload']}")

        mqtt_client.publish(command['topic'], json.dumps(command['payload']))
        commands_sent.append({
            "command": command,
            "timestamp": time.time()
        })

        return True
    except Exception as e:
        logger.error(f"Error sending command: {e}")
        return False

def run_tests():
    """Spustí všechny testy"""
    logger.info("🚀 Starting MQTT tests...")

    # Počkej na připojení
    time.sleep(2)

    for i, command in enumerate(TEST_COMMANDS, 1):
        logger.info(f"\n--- TEST {i}/{len(TEST_COMMANDS)} ---")

        if send_test_command(command):
            # Počkej na odpověď
            time.sleep(3)
        else:
            logger.error(f"Failed to send test {i}")

    # Počkej na posledních odpovědi
    time.sleep(5)

    # Vyhodnoť výsledky
    logger.info(f"\n🏁 TEST RESULTS:")
    logger.info(f"Commands sent: {len(commands_sent)}")
    logger.info(f"Responses received: {len(responses_received)}")

    if len(responses_received) == len(commands_sent):
        logger.info("✅ ALL TESTS PASSED - All commands received responses!")
    else:
        logger.warning(f"⚠️  SOME TESTS FAILED - Missing {len(commands_sent) - len(responses_received)} responses")

    # Detailní výpis odpovědí
    logger.info("\n📋 DETAILED RESPONSES:")
    for i, response in enumerate(responses_received, 1):
        logger.info(f"{i}. {response['topic']}: {response['payload']}")

# =============================
# HLAVNÍ KÓD
# =============================
def main():
    global mqtt_client

    mqtt_client = mqtt.Client(client_id=MQTT_CLIENT_ID)
    mqtt_client.username_pw_set(MQTT_USERNAME, MQTT_PASSWORD)
    mqtt_client.tls_set()  # TLS pro 8883

    mqtt_client.on_connect = on_connect
    mqtt_client.on_message = on_message

    logger.info("Starting MQTT test server...")
    mqtt_client.connect(MQTT_HOST, MQTT_PORT, keepalive=60)

    # Spusť MQTT loop v separátním threadu
    mqtt_client.loop_start()

    # Spusť testy v separátním threadu
    test_thread = threading.Thread(target=run_tests)
    test_thread.daemon = True
    test_thread.start()

    try:
        # Hlavní thread čeká
        test_thread.join()

        # Nech server běžet pro další zprávy
        logger.info("\n🔄 Test completed. Server continues listening for responses...")
        logger.info("Press Ctrl+C to exit")

        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        logger.info("\n👋 Shutting down test server...")
        mqtt_client.loop_stop()
        mqtt_client.disconnect()

if __name__ == "__main__":
    main()
